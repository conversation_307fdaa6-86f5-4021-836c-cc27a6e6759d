import { suite, test } from "mocha-typescript";
import { GameFlowContext } from "../../skywind/services/context/gamecontext";
import {
    createGameToken,
    flushAll,
    getGameHistoryItem,
    getRoundHistory,
    getSessionHistory,
    syncModels,
    TEST_MODULE_NAME
} from "../helper";
import { expect } from "chai";
import { GameContextID } from "../../skywind/services/contextIds";
import config from "../../skywind/config";
import { GameFlowContextImpl } from "../../skywind/services/context/gameContextImpl";
import { PaymentOperation } from "../../skywind/services/wallet";
import { getGameContextEncoder } from "../../skywind/services/encoder/contextEncoding";
import { GameFlowInfoImpl } from "../../skywind/services/context/gameFlowInfo";
import { getGameFlowContextManager } from "../../skywind/services/contextmanager/contextManagerImpl";
import { Currency } from "@skywind-group/sw-game-core";
import { GameData } from "../../skywind/services/auth";
import { GameTokenData } from "../../skywind/services/tokens";
import { GameSession } from "../../skywind/services/gameSession";
import * as _ from "lodash";

@suite("GameContext in 'fun_bonus' mode")
class GameContextFunBonusModeSpec {
    public readonly contextManager = getGameFlowContextManager("fun_bonus");
    public readonly currency: Currency = 0;
    public readonly settings = {
        coins: [3, 4],
        defaultCoin: 4,
        maxTotalStake: this.currency,
        stakeAll: [1, 2, 3, 4],
        stakeDef: this.currency,
        stakeMax: this.currency,
        stakeMin: this.currency,
        winMax: this.currency,
        currencyMultiplier: 100,
    };

    public gameID: GameContextID = GameContextID.create("gameId", 1, "playerId", "deviceId", "fun_bonus");

    public readonly gameData: GameData = {
        gameTokenData: undefined,
        limits: this.settings,
        gameId: "gameId",
    };

    public readonly gameTokenData: GameTokenData = {
        playerCode: this.gameID.playerCode,
        gameCode: "gameId",
        brandId: this.gameID.brandId,
        currency: "USD",
        playmode: "fun_bonus"
    };

    public sessionId: GameSession;
    public newSessionId: GameSession;

    public async before() {
        this.gameTokenData.token = await createGameToken(this.gameTokenData);
        this.gameData.gameTokenData = this.gameTokenData;
        await flushAll();
        await syncModels();
        this.sessionId = await GameSession.generate(this.gameID, "fun_bonus");
        this.newSessionId = await GameSession.generate(this.gameID, "fun_bonus");
    }

    @test("is created")
    public async testCreated() {
        let context: GameFlowContext = await this.contextManager
            .findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);
        const expected = {
            currentScene: "scene",
        };

        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).is.undefined;
        expect(context.playerContext).is.not.undefined;
        expect(context.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:fun_bonus:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "fun_bonus"
            }
        ]);
        expect(context.playerContext.brokenGames).deep.equal([]);

        await context.update(expected);
        context = await this.contextManager.findGameContextById(context.id);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(expected);
        expect(context.playerContext).is.not.undefined;
        expect(context.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:fun_bonus:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "fun_bonus"
            }
        ]);

        const sessions = await getSessionHistory(0, 100);
        expect(sessions.length).equal(0);
    }

    @test("is created from copy")
    public async testCreatedFromCopy() {
        const id = GameContextID.createFromString(
            config.namespaces.contextPrefix.fun_bonus + ":1:playerId:gameId:deviceId");
        const context: GameFlowContextImpl = new GameFlowContextImpl(id);
        context.gameVersion = "1";
        context.gameContext = {
            scenesState: {},
            currentScene: "scene",
            nextScene: "next",
            history: [],
            stake: null,
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };
        context.session = this.sessionId;
        context.lastRequestId = 1;
        context.gameSerialNumber = 1;
        context.totalEventId = 1;
        context.settings = {
            coins: [3, 4],
            defaultCoin: 4,
            maxTotalStake: 0,
            stakeAll: [1, 2, 3, 4],
            stakeDef: 0,
            stakeMax: 0,
            stakeMin: 0,
            winMax: 0,
            currencyMultiplier: 100,
            transferEnabled: false,
        };
        context.version = 3;
        context.gameData = this.gameData;
        context.pendingModification = {
            history: {
                type: "slot",
                roundEnded: true,
                data: { positions: [1, 2, 3] }
            },
            walletOperation: {
                operation: "payment",
                transactionId: "trxId",
                currency: "USD",
                bet: 100,
                win: 1000,
            } as PaymentOperation,
            newState: {},
        };

        context.roundEnded = false;
        context.roundId = "1";
        context.createdAt = new Date();
        context.updatedAt = new Date();
        context.round = {
            totalBet: 1,
            totalWin: 2,
            totalEvents: 3,
            balanceBefore: 4,
            balanceAfter: 5
        };

        const newSettings = {
            coins: [5, 6],
            defaultCoin: 5,
            maxTotalStake: 0,
            stakeAll: [5, 6, 7, 8],
            stakeDef: 0,
            stakeMax: 0,
            stakeMin: 0,
            winMax: 0,
            currencyMultiplier: 1000,
            transferEnabled: false,
        };

        const copy = await this.contextManager.createContextFrom(id,
            context,
            this.newSessionId,
            this.gameData,
            TEST_MODULE_NAME,
            newSettings);

        const expected = {
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": this.newSessionId,
            "gameData": this.gameData,
            "id": context.id,
            "lockExclusively": false,
            "gameSerialNumber": 1,
            "totalEventId": 1,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "gameVersion": "0.1.0",
            "reactive": true,
            "roundEnded": false,
            "roundId": "1",
            "pendingModification": context.pendingModification,
            "gameContext": context.gameContext,
            "settings": newSettings,
            "specialState": undefined,
            "version": 4,
            "jpContext": undefined,
            "persistencePolicy": 0,
            "round": context.round,
        };

        expect(_.omit(copy, "createdAt", "updatedAt", "requestContext")).deep.equal(expected);

        const sessions = await getSessionHistory(0, 100);
        expect(sessions.length).equal(0);
    }

    @test("is created from old copy")
    public async testCreatedFromOldCopy() {
        const id = GameContextID.createFromString(
            config.namespaces.contextPrefix.fun_bonus + ":1:playerId:gameId:deviceId");
        const context: GameFlowContextImpl = new GameFlowContextImpl(id);
        context.gameVersion = undefined;
        context.gameContext = {
            scenesState: {},
            currentScene: "scene",
            nextScene: "next",
            history: [],
            stake: null,
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };
        context.session = this.sessionId;
        context.lastRequestId = 1;
        context.gameSerialNumber = 1;
        context.totalEventId = 1;
        context.settings = {
            coins: [3, 4],
            defaultCoin: 4,
            maxTotalStake: 0,
            stakeAll: [1, 2, 3, 4],
            stakeDef: 0,
            stakeMax: 0,
            stakeMin: 0,
            winMax: 0,
            currencyMultiplier: 100,
            transferEnabled: false,
        };
        context.version = 3;
        context.gameData = this.gameData;
        context.pendingModification = {
            history: {
                type: "slot",
                roundEnded: true,
                data: { positions: [1, 2, 3] }
            },
            walletOperation: {
                actions: [
                    { action: "debit", amount: 100, attribute: "balance" },
                    { action: "credit", amount: 1000, attribute: "balance" }
                ]
            } as any,
            newState: {},
        };

        context.roundEnded = false;
        context.roundId = "1";
        context.createdAt = new Date();
        context.updatedAt = new Date();

        const newSettings = {
            coins: [5, 6],
            defaultCoin: 5,
            maxTotalStake: 0,
            stakeAll: [5, 6, 7, 8],
            stakeDef: 0,
            stakeMax: 0,
            stakeMin: 0,
            winMax: 0,
            currencyMultiplier: 1000,
            transferEnabled: false,
        };

        const copy = await this.contextManager.createContextFrom(id,
            await getGameContextEncoder().encode(context),
            this.newSessionId,
            this.gameData,
            TEST_MODULE_NAME,
            newSettings);

        const expected = {
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": this.newSessionId,
            "gameData": this.gameData,
            "id": context.id,
            "lockExclusively": false,
            "gameSerialNumber": 1,
            "totalEventId": 1,
            "gameVersion": "0.1.0",
            "roundEnded": false,
            "roundId": "1",
            "pendingModification": {
                "history": {
                    "type": "slot",
                    "roundEnded": true,
                    "data": {
                        "positions": [
                            1,
                            2,
                            3
                        ]
                    }
                },
                "walletOperation": {
                    "actions": [
                        {
                            "action": "debit",
                            "attribute": "balance",
                            "amount": 100
                        },
                        {
                            "action": "credit",
                            "attribute": "balance",
                            "amount": 1000
                        }
                    ]
                },
                "newState": {}
            },
            "jackpotPending": undefined,
            "round": undefined,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "persistencePolicy": 0,
            "jpContext": undefined,
            "reactive": true,
            "gameContext": context.gameContext,
            "settings": newSettings,
            "specialState": undefined,
            "version": 4,
        };
        expect(_.omit(copy, "createdAt", "updatedAt", "requestContext")).deep.equal(expected);

        const sessions = await getSessionHistory(0, 100);
        expect(sessions.length).equal(0);
    }

    @test("is updated successfully")
    public async testUpdateSuccessFull() {
        await this.contextManager.findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);
        const context: GameFlowContextImpl = (await this.contextManager.findGameContextById(this.gameID));
        const state = {
            currentScene: "sceneTest",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}

                }
            },
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };
        context.lastRequestId = 2;

        await context.update(state);

        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state);
        expect(context.playerContext).is.not.undefined;
        expect(context.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:fun_bonus:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "fun_bonus"
            }
        ]);
    }

    @test("is updated & committed pending modification")
    public async testUpdateAdnCommitPending() {
        await this.contextManager.findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);
        let context = await this.contextManager.findGameContextById(this.gameID);
        const request = { request: "req", requestId: 1 };
        const state1 = {
            currentScene: "sceneTest",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}

                }
            },
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };

        const history1: any = {
            type: "slot",
            roundEnded: true,
            data: {
                positions: [1, 2, 3],
            }
        };

        const walletOperation1: any = {
            operation: "payment",
            transactionId: "1",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: history1.roundEnded,
            bet: 1900,
            win: 200,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation1, state1, request, history1);

        const expected = {
            id: this.gameID,
            lastRequestId: 0,
        };

        context = await this.contextManager.findGameContextById(this.gameID);
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).is.undefined;
        walletOperation1.ts = walletOperation1.ts.toISOString();
        expect(context.pendingModification).deep.equal({
            "history": history1,
            "newState": state1,
            "walletOperation": walletOperation1,
        });

        await context.commitPendingModification();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state1);
        expect(context.pendingModification).is.undefined;

        let result: any = await getGameHistoryItem();
        expect(result).is.not.undefined;

        const history2: any = {
            type: "slot",
            roundEnded: true,
            data: {
                positions: [1, 2],
            }
        };

        const state2 = _.cloneDeep(state1);
        state2["nextScene"] = "SOMESCENE2";

        const walletOperation2: PaymentOperation = {
            operation: "payment",
            transactionId: "2",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.id,
            roundEnded: history2.roundEnded,
            bet: 2000,
            win: 100,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation2, state2, request, history2);

        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameContext).deep.equal(state1);
        expect(context.pendingModification).deep.equal({
            "analytics": undefined,
            "history": history2,
            "newState": state2,
            "walletOperation": walletOperation2,
        });

        await context.commitPendingModification();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state2);
        expect(context.pendingModification).is.undefined;

        result = await getGameHistoryItem();

        expect(result).is.not.undefined;
        const history3: any = {
            type: "slot",
            roundEnded: true,
            data: {
                positions: [1, 2, 3, 4],
            }
        };

        const state3 = _.cloneDeep(state2);
        state3["nextScene"] = "SOMESCENE3";
        const walletOperation3: PaymentOperation = {
            operation: "payment",
            transactionId: "3",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.id,
            roundEnded: history3.roundEnded,
            bet: 100,
            win: 200,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation3, state3, request, history3);

        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameContext).deep.equal(state2);

        expect(context.pendingModification).deep.equal({
            "analytics": undefined,
            "history": history3,
            "newState": state3,
            "walletOperation": walletOperation3,
        });

        await context.commitPendingModification();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state3);
        expect(context.pendingModification).undefined;

        result = await getGameHistoryItem();
        expect(result).is.not.undefined;

        const rounds = await getRoundHistory(0, 1000);
        expect(rounds.length).equal(3);

        // Verify the round history contains the expected data
        const roundsWithoutTimestamps = rounds.map((item) => _.omit(item, "finishedAt", "startedAt", "ctrl"));
        expect(roundsWithoutTimestamps).to.deep.include.members([
            {
                "brandId": 1,
                "broken": false,
                "currency": "USD",
                "deviceId": "deviceId",
                "gameCode": "gameId",
                "gameId": "gameId",
                "id": "0",
                "playerCode": "playerId",
                "sessionId": "0",
                "totalBet": 1900,
                "totalEvents": 1,
                "totalJpContribution": 0,
                "totalJpWin": 0,
                "totalWin": 200
            },
            {
                "brandId": 1,
                "broken": false,
                "currency": "USD",
                "deviceId": "deviceId",
                "gameCode": "gameId",
                "gameId": "gameId",
                "id": "1",
                "playerCode": "playerId",
                "sessionId": "0",
                "totalBet": 2000,
                "totalEvents": 1,
                "totalJpContribution": 0,
                "totalJpWin": 0,
                "totalWin": 100
            },
            {
                "brandId": 1,
                "broken": false,
                "currency": "USD",
                "deviceId": "deviceId",
                "gameCode": "gameId",
                "gameId": "gameId",
                "id": "2",
                "playerCode": "playerId",
                "sessionId": "0",
                "totalBet": 100,
                "totalEvents": 1,
                "totalJpContribution": 0,
                "totalJpWin": 0,
                "totalWin": 200
            }
        ]);
    }

    @test("is updated & rollbacked pending modification")
    public async testUpdateAndRollbackPendingModification() {
        await this.contextManager.findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);
        const context = await this.contextManager.findGameContextById(this.gameID);

        const state1 = {
            currentScene: "sceneTest",
            state: {
                multiplier: 1,
                behaviorsState: {}
            }
        };
        context.lastRequestId = 2;

        const request = { request: "req", requestId: 1 };

        const history1: any = {
            type: "slot",
            version: 1,
            roundEnded: true,
            data: {
                positions: [1, 2, 3, 4],
            }
        };

        const walletOperation1: PaymentOperation = {
            operation: "payment",
            transactionId: "1",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.id,
            roundEnded: history1.roundEnded,
            bet: 1900,
            win: 200,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation1, state1, request, history1, {
            jackpotOperation: {
                type: "contribution",
                payload: {
                    transactionId: "jpn_trx_id",
                    amount: 1000,
                    roundId: "1"
                }
            }
        });

        expect(context).contain({
            id: this.gameID,
            lastRequestId: 2,
        });
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).is.undefined;
        expect(context.pendingModification).deep.equal({
            "analytics": undefined,
            "history": history1,
            "newState": state1,
            "walletOperation": walletOperation1,
        });

        expect(context.jackpotPending).deep.equal({
            jackpotOperation: {
                type: "contribution",
                payload: {
                    transactionId: "jpn_trx_id",
                    amount: 1000,
                    roundId: "1"
                }
            }
        });

        expect(context.roundId).equals("0");
        await context.rollbackPendingModification();
        expect(context).contain({
            id: this.gameID,
            lastRequestId: 2,
        });
        expect(context.roundId).equals("1");
        expect(context.session).to.not.be.empty;
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).is.undefined;
        expect(context.pendingModification).is.undefined;
        expect(context.jackpotPending).is.undefined;
        expect(context.playerContext).is.not.undefined;
        expect(context.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:fun_bonus:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "fun_bonus"
            }
        ]);

        const result: any = await getGameHistoryItem();
        expect(result).is.undefined;
    }

    @test("is found by playerCode, gameID, deviceId and brandID")
    public async testFoundByPlayerCodeGameIdDeviceIdAndBrandId() {
        const context1: GameFlowContext = await this.contextManager
            .findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);
        context1.lastRequestId = 2;
        const state = {
            multiplier: 1,
            behaviorsState: {},
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true,
            }
        };

        await context1.update(state);
        const context2: GameFlowContext = await this.contextManager.findGameContextById(this.gameID);

        const expected = {
            id: this.gameID,
            lastRequestId: 2,
        };

        expect(context2).contain(expected);
        expect(context2.session.id).equals(this.sessionId.id);
        expect(context2.gameData).deep.equal(this.gameData);
        expect(context2.settings).deep.equal(this.settings);
        expect(context2.gameContext).deep.equal(state);
        expect(context2.playerContext).is.not.undefined;
        expect(context2.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:fun_bonus:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "fun_bonus"
            }
        ]);
    }

    @test("is deleted")
    public async testDelete() {
        const context: GameFlowContextImpl = await this.contextManager.findOrCreateGameContext(this.gameID,
            this.sessionId,
            this.gameData,
            TEST_MODULE_NAME);
        const expected = {
            id: this.gameID,
            session: this.sessionId,
            lastRequestId: 0,
        };
        expect(context).contain(expected);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.settings).deep.equal(this.settings);
        expect(context.playerContext).is.not.undefined;
        expect(context.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:fun_bonus:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "fun_bonus"
            }
        ]);
        await context.remove();
        expect(await this.contextManager.findGameContextById(this.gameID)).is.undefined;

        const [__, playerCtx] = await this.contextManager.findContexts(this.gameID);
        expect(playerCtx).is.not.undefined;
        expect(playerCtx.activeGames).deep.equal([]);
        expect(playerCtx.brokenGames).deep.equal([]);
    }

    @test("is deleted and session history is stored")
    public async testDeleteAndStoreSessionHistory() {
        const context: GameFlowContext = await this.contextManager.findOrCreateGameContext(this.gameID, this.sessionId,
            this.gameData, TEST_MODULE_NAME);
        const expected = {
            id: this.gameID,
            session: this.sessionId,
            lastRequestId: 0,
        };
        expect(context).contain(expected);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.settings).deep.equal(this.settings);
        await context.remove();

        const [ctx, playerCtx] = await this.contextManager.findContexts(this.gameID);
        expect(ctx).is.undefined;
        expect(playerCtx).is.not.undefined;
        expect(playerCtx.activeGames).deep.equal([]);
        expect(playerCtx.brokenGames).deep.equal([]);

        const sessions = await getSessionHistory(0, 100);
        expect(sessions.length).equal(1);
    }

    @test("is deleted and round isn't stored because there is no round statistic in context")
    public async testDeleteAndNotStoreRoundWithoutStat() {
        let context: GameFlowContextImpl = await this.contextManager
            .findOrCreateGameContext(this.gameID,
                this.sessionId,
                this.gameData,
                TEST_MODULE_NAME);
        const expected = {
            id: this.gameID,
            lastRequestId: 0,
        };

        context.roundEnded = false;
        await context.update();
        context = (await this.contextManager.findGameContextById(this.gameID)) as GameFlowContextImpl;
        expect(context).contain(expected);
        expect(context.session.id).equals(this.sessionId.id);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.settings).deep.equal(this.settings);

        await context.remove();

        const rounds = await getRoundHistory(0, 100);
        expect(rounds.length).equal(0);

        expect(await this.contextManager.findGameContextById(this.gameID)).is.undefined;
    }

    @test("is deleted and round is stored")
    public async testDeleteAndStoreRound() {
        let context: GameFlowContextImpl = await this.contextManager
            .findOrCreateGameContext(this.gameID,
                this.sessionId,
                this.gameData,
                TEST_MODULE_NAME);

        context.roundEnded = false;
        const ts = new Date();
        await context.update();
        context = await this.contextManager.findGameContextById(this.gameID);
        const expected = {
            id: this.gameID,
            lastRequestId: 0,
        };
        expect(context).contain(expected);
        expect(context.session.id).equal(this.sessionId.id);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.settings).deep.equal(this.settings);

        context.round = {
            totalBet: 100,
            totalWin: 20,
            totalEvents: 3,
            balanceBefore: 200,
            balanceAfter: 120,
            broken: true,
            startedAt: ts,
        };
        await context.remove(true);

        const [ctx, playerCtx] = await this.contextManager.findContexts(this.gameID);
        expect(ctx).is.undefined;
        expect(playerCtx).is.not.undefined;
        expect(playerCtx.activeGames).deep.equal([]);
        expect(playerCtx.brokenGames).deep.equal([]);

        const rounds = await getRoundHistory(0, 100);
        expect(rounds).deep.equal([{
            "balanceAfter": 120,
            "balanceBefore": 200,
            "brandId": 1,
            "broken": true,
            "currency": "USD",
            "deviceId": "deviceId",

            "gameCode": "gameId",
            "gameId": "gameId",
            "playerCode": "playerId",
            "sessionId": this.sessionId.sessionId,
            "id": "0",
            "startedAt": ts.toISOString(),
            "totalBet": 100,
            "totalEvents": 3,
            "totalWin": 20,
            "ctrl": rounds[0].ctrl
        }]);
    }

    @test("isn't found because not exists")
    public async testNotFound() {
        expect(await this.contextManager.findGameContextById(GameContextID.create("gameId",
            1,
            "playerId",
            "deviceId"))).is.undefined;
    }
}
