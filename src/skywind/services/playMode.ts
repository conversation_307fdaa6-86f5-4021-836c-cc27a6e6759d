import { Currencies } from "@skywind-group/sw-currency-exchange";
import { GameMode } from "@skywind-group/sw-game-core";
import {
    createForceFinishGameEvent,
    createRoundHistoryEvent,
    GameEventHistory,
    RoundHistory
} from "../history/history";
import { BNSGameData, GameData } from "./auth";
import { GameFlowContextImpl } from "./context/gameContextImpl";
import { getCurrencyExchange } from "./currencyexchange";
import { RecoveryType } from "./offlinestorage/offlineCommands";
import { GameTokenData } from "./tokens";

const BNS_CURRENCY = "BNS";

export class PlayMode {
    public static readonly GAME_MODES: GameMode[] = ["real", "bns", "play_money", "fun", "fun_bonus"];

    /**
     * Checks if a GameMode supports jackpot functionality
     */
    public static supportJP(gameMode: GameMode) {
        return gameMode === "real" || !gameMode;
    }

    // todo should be removed eventully
    public static getModeByCurrency(currency: string): GameMode {
        if (currency === BNS_CURRENCY) {
            return "bns";
        }
        if (Currencies.value(currency)?.funBonus) {
            return "fun_bonus";
        }
        return "real";
    }

    public static getCurrency(gameTokenData: GameTokenData) {
        if (gameTokenData.playmode === "bns") {
            return BNS_CURRENCY;
        } else {
            return gameTokenData.currency;
        }
    }

    public static clearContextAndCreateHistoryIfNeeded(context: GameFlowContextImpl, gameData: GameData):
        { roundHistory: RoundHistory, gameEventHistory: GameEventHistory } {
        let roundHistory;
        let gameEventHistory;
        if (gameData.gameTokenData.playmode === "bns") {
            const currentData = gameData as BNSGameData;
            const lastData = context.gameData as BNSGameData;
            if (lastData?.bnsPromotion?.promoId !== currentData?.bnsPromotion?.promoId) {
                if (context?.unfinished) {
                    roundHistory = createRoundHistoryEvent(context);
                    roundHistory.recoveryType = "finalize";
                    roundHistory.finishedAt = new Date();
                    gameEventHistory = createForceFinishGameEvent(context, RecoveryType.FINALIZE);
                }
                context.round = undefined;
                context.roundId = undefined;
                context.roundEnded = true;
                context.gameContext = undefined;
            }
        }
        return { roundHistory, gameEventHistory };
    }

    public static exchange(gameData: GameData, amount: number, targetCurrency: string, baseCurrency?: string): number {
        const playerCurrency = gameData.gameTokenData.currency;
        if (gameData.gameTokenData.playmode === "bns"
            && (baseCurrency === BNS_CURRENCY || targetCurrency === BNS_CURRENCY)) {
            const bnsData = gameData as BNSGameData;
            const exchangeRate = bnsData.bnsPromotion.exchangeRate;
            let result = amount;
            if (baseCurrency === BNS_CURRENCY || !baseCurrency) {
                result = getCurrencyExchange()
                    .exchange(result,
                        BNS_CURRENCY,
                        playerCurrency,
                        { [BNS_CURRENCY]: { [playerCurrency]: exchangeRate } });

            } else {
                result = getCurrencyExchange()
                    .exchange(result, baseCurrency, playerCurrency);
            }

            if (targetCurrency === BNS_CURRENCY) {
                result = getCurrencyExchange()
                    .exchange(result,
                        playerCurrency,
                        BNS_CURRENCY,
                        { [playerCurrency]: { [BNS_CURRENCY]: 1 / exchangeRate } });
            } else {
                result = getCurrencyExchange()
                    .exchange(result, playerCurrency, targetCurrency);
            }

            return result;
        } else if (gameData.gameTokenData.playmode === "play_money") {
            if (Currencies.get(targetCurrency).isSocial) {
                return getCurrencyExchange()
                    .exchange(amount, baseCurrency || playerCurrency, targetCurrency);
            } else {
                return amount;
            }
        }
        return getCurrencyExchange().exchange(amount, baseCurrency || playerCurrency, targetCurrency);
    }

    /**
     * Checks if a GameMode supports split payments
     */
    public static supportsSplitPayment(gameMode: GameMode): boolean {
        return gameMode !== "bns";
    }

    /**
     * Checks if a GameMode supports revert operations
     */
    public static supportsRevert(gameMode: GameMode): boolean {
        return gameMode !== "bns";
    }

    /**
     * Checks if a GameMode supports history storage (round and session history)
     */
    public static supportsHistory(gameMode: GameMode): boolean {
        return gameMode !== "play_money" && gameMode !== "fun_bonus";
    }
}
